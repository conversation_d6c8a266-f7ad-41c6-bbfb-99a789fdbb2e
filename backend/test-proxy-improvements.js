const ProxyService = require('./src/services/ProxyService');

async function testProxyImprovements() {
  const proxyService = new ProxyService();
  
  console.log('🚀 Testing improved proxy functionality...\n');
  console.log('📊 Available IP checkers:', Object.keys(proxyService.ipCheckerEndpoints).join(', '));
  console.log('🔄 Fallback order:', proxyService.fallbackOrder.join(' → '));
  console.log('⏱️  Timeout:', proxyService.retryConfig.timeout / 1000, 'seconds');
  console.log('🔁 Max retries:', proxyService.retryConfig.maxRetries);
  console.log('');

  // Test cases focusing on real-world scenarios
  const testCases = [
    {
      name: 'Direct Connection (No Proxy)',
      proxy: { type: 'No proxy' },
      shouldWork: true
    },
    {
      name: 'HTTP Proxy - Free Public Proxy',
      proxy: {
        type: 'HTTP',
        host: '***************',
        port: 80
      },
      shouldWork: false, // Most free proxies are unreliable
      note: 'Free proxy - may not work'
    },
    {
      name: 'SOCKS5 Proxy (No Auth) - Example',
      proxy: {
        type: 'SOCKS5',
        host: '127.0.0.1',
        port: 1080
      },
      shouldWork: false,
      note: 'Local SOCKS5 - will fail unless running locally'
    },
    {
      name: 'SOCKS5 Proxy (With Auth) - Example',
      proxy: {
        type: 'SOCKS5',
        host: '127.0.0.1',
        port: 1080,
        username: 'testuser',
        password: 'testpass'
      },
      shouldWork: false,
      note: 'SOCKS5 with auth - uses fallback method'
    },
    {
      name: 'Invalid Proxy Configuration',
      proxy: {
        type: 'HTTP',
        host: '',
        port: 8080
      },
      shouldWork: false,
      note: 'Invalid config - should fail gracefully'
    }
  ];

  for (const testCase of testCases) {
    console.log(`📋 Testing: ${testCase.name}`);
    if (testCase.note) {
      console.log(`   💡 Note: ${testCase.note}`);
    }
    
    console.log(`   Type: ${testCase.proxy.type}`);
    if (testCase.proxy.host) {
      console.log(`   Host: ${testCase.proxy.host}:${testCase.proxy.port}`);
      if (testCase.proxy.username) {
        console.log(`   Auth: ${testCase.proxy.username}:${'*'.repeat(testCase.proxy.password?.length || 0)}`);
      }
    }

    try {
      // Test proxy configuration creation
      if (testCase.proxy.type !== 'No proxy') {
        console.log(`   🔧 Creating proxy configuration...`);
        try {
          const config = proxyService.createProxyConfig(testCase.proxy);
          console.log(`   ✅ Config created: ${config.server}`);
          if (config.username) {
            console.log(`   🔐 Auth configured: ${config.username}:***`);
          }
          if (config.bypass) {
            console.log(`   🚫 Bypass: ${config.bypass}`);
          }
        } catch (configError) {
          console.log(`   ⚠️  Config creation failed: ${configError.message}`);
          if (!testCase.shouldWork) {
            console.log(`   ✅ Expected failure - good!`);
          }
          continue;
        }
      }

      // Test proxy connection with improved error handling
      console.log(`   🔄 Testing connection with fallback IP checkers...`);
      const startTime = Date.now();
      
      const result = await proxyService.testProxyConnection(testCase.proxy, 'ip-api');
      const duration = Date.now() - startTime;
      
      if (result.isActive) {
        console.log(`   ✅ Connection successful! (${duration}ms)`);
        if (result.attempt) {
          console.log(`   🎯 Succeeded on attempt: ${result.attempt}`);
        }
        if (result.result) {
          console.log(`   📍 IP: ${result.result.ip || 'Unknown'}`);
          console.log(`   🌍 Country: ${result.result.country || 'Unknown'}`);
          console.log(`   🏙️  City: ${result.result.city || 'Unknown'}`);
          console.log(`   🕐 Timezone: ${result.result.timezone || 'Unknown'}`);
          console.log(`   🌐 ISP: ${result.result.isp || 'Unknown'}`);
        }
        
        if (!testCase.shouldWork) {
          console.log(`   🎉 Unexpected success - proxy is working!`);
        }
      } else {
        console.log(`   ❌ Connection failed: ${result.error || 'Unknown error'} (${duration}ms)`);
        if (testCase.shouldWork) {
          console.log(`   ⚠️  Expected to work but failed`);
        } else {
          console.log(`   ✅ Expected failure - error handling working correctly`);
        }
      }

    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      if (!testCase.shouldWork) {
        console.log(`   ✅ Expected error - good error handling!`);
      }
    }
    
    console.log(`   ${'─'.repeat(60)}`);
  }

  // Test IP checker fallback specifically
  console.log('\n🔄 Testing IP checker fallback mechanism...');
  try {
    const proxyService2 = new ProxyService();
    // Temporarily break IP2Location to test fallback
    proxyService2.ipCheckerEndpoints['IP2Location'] = 'https://invalid-url-that-will-fail.com/';
    
    const result = await proxyService2.testProxyConnection({ type: 'No proxy' }, 'IP2Location');
    if (result.isActive) {
      console.log('✅ Fallback mechanism working - got IP info despite broken primary checker');
      console.log(`📍 IP: ${result.result.ip}`);
    } else {
      console.log('❌ Fallback mechanism failed');
    }
  } catch (error) {
    console.log(`❌ Fallback test error: ${error.message}`);
  }

  console.log('\n✅ Proxy improvements testing completed!');
  console.log('\n📝 Summary of improvements:');
  console.log('   • Retry mechanism with configurable attempts');
  console.log('   • Fallback IP checker services');
  console.log('   • Better error handling and reporting');
  console.log('   • Improved SOCKS5 authentication support');
  console.log('   • Enhanced timeout configuration');
  console.log('   • More robust proxy configuration validation');
}

// Run the test
if (require.main === module) {
  testProxyImprovements().catch(console.error);
}

module.exports = testProxyImprovements;
