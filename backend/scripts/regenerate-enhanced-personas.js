#!/usr/bin/env node

/**
 * Regenerate <PERSON>hanced Personas Script
 * Tạo lại ngân hàng personas với các tính năng mới để tránh Google detection
 */

const fs = require('fs').promises;
const path = require('path');
const PersonaGenerator = require('../src/antidetect/persona-generator');

async function regenerateEnhancedPersonas() {
  console.log('🎭 Regenerating enhanced personas for Google detection evasion...\n');

  const generator = new PersonaGenerator();
  const personas = [];

  // Regions mở rộng để tạo personas
  const regions = ['US', 'GB', 'DE', 'FR', 'CA', 'AU', 'NL', 'SE', 'NO', 'DK', 'IT', 'ES', 'JP', 'KR', 'BR'];
  const personasPerRegion = 10; // 10 personas per region = 150 total

  for (const region of regions) {
    console.log(`Generating enhanced personas for region: ${region}`);
    
    for (let i = 0; i < personasPerRegion; i++) {
      // Tạo 65% Windows, 30% macOS, 5% Linux (for diversity)
      const rand = Math.random();
      let persona;
      
      if (rand < 0.65) {
        persona = generator.generateWindowsPersona(region);
      } else if (rand < 0.95) {
        persona = generator.generateMacPersona(region);
      } else {
        // Generate Linux persona (basic implementation)
        persona = generator.generateWindowsPersona(region);
        persona.platform = 'Linux';
        persona.platformVersion = 'X11';
        persona.userAgent = persona.userAgent.replace(/Windows NT \d+\.\d+/, 'X11; Linux x86_64');
      }
      
      // Add enhanced properties for better detection evasion
      persona.enhanced = {
        createdAt: new Date().toISOString(),
        version: '2.0',
        features: [
          'enhanced-webgl',
          'canvas-noise',
          'audio-fingerprint-protection',
          'navigator-spoofing',
          'google-evasion',
          'behavioral-patterns',
          'timezone-consistency',
          'http-headers-spoofing'
        ]
      };
      
      // Add more realistic network properties
      persona.network = {
        effectiveType: ['4g', '3g', 'slow-2g'][Math.floor(Math.random() * 3)],
        downlink: Math.random() * 20 + 5, // 5-25 Mbps
        rtt: Math.random() * 100 + 20 // 20-120ms
      };
      
      // Enhance WebGL properties if missing
      if (!persona.webgl.maxTextureSize) {
        persona.webgl.maxTextureSize = 16384;
        persona.webgl.maxVertexAttribs = 16;
        persona.webgl.maxVaryingVectors = 30;
        persona.webgl.aliasedLineWidthRange = [1, 1];
        persona.webgl.aliasedPointSizeRange = [1, 1024];
      }
      
      // Add region info
      persona.region = region;
      
      personas.push(persona);
    }
    
    console.log(`✅ Generated ${personasPerRegion} enhanced personas for ${region}`);
  }

  // Backup existing personas if they exist
  const outputPath = path.join(__dirname, '../data/personas.json');
  const backupPath = path.join(__dirname, '../data/personas-backup.json');
  
  try {
    const existingData = await fs.readFile(outputPath, 'utf8');
    await fs.writeFile(backupPath, existingData);
    console.log(`📦 Backed up existing personas to: ${backupPath}`);
  } catch (error) {
    console.log('📦 No existing personas to backup');
  }

  // Lưu personas mới vào file
  await fs.writeFile(outputPath, JSON.stringify(personas, null, 2));

  console.log(`\n🎉 Successfully regenerated ${personas.length} enhanced personas!`);
  console.log(`📁 Saved to: ${outputPath}`);
  
  // Thống kê chi tiết
  const windowsCount = personas.filter(p => p.platform === 'Windows').length;
  const macosCount = personas.filter(p => p.platform === 'macOS').length;
  const linuxCount = personas.filter(p => p.platform === 'Linux').length;
  
  const chromeCount = personas.filter(p => p.userAgent.includes('Chrome')).length;
  const firefoxCount = personas.filter(p => p.userAgent.includes('Firefox')).length;
  const edgeCount = personas.filter(p => p.userAgent.includes('Edg')).length;
  const safariCount = personas.filter(p => p.userAgent.includes('Safari') && !p.userAgent.includes('Chrome')).length;
  
  const nvidiaCount = personas.filter(p => p.webgl.vendor.includes('NVIDIA')).length;
  const amdCount = personas.filter(p => p.webgl.vendor.includes('AMD')).length;
  const intelCount = personas.filter(p => p.webgl.vendor.includes('Intel')).length;
  const appleCount = personas.filter(p => p.webgl.vendor.includes('Apple')).length;
  
  console.log('\n📊 Enhanced Statistics:');
  console.log('Platforms:');
  console.log(`   Windows: ${windowsCount}`);
  console.log(`   macOS: ${macosCount}`);
  console.log(`   Linux: ${linuxCount}`);
  
  console.log('Browsers:');
  console.log(`   Chrome: ${chromeCount}`);
  console.log(`   Firefox: ${firefoxCount}`);
  console.log(`   Edge: ${edgeCount}`);
  console.log(`   Safari: ${safariCount}`);
  
  console.log('GPU Vendors:');
  console.log(`   NVIDIA: ${nvidiaCount}`);
  console.log(`   AMD: ${amdCount}`);
  console.log(`   Intel: ${intelCount}`);
  console.log(`   Apple: ${appleCount}`);
  
  console.log('Regions:');
  regions.forEach(region => {
    const count = personas.filter(p => p.region === region).length;
    console.log(`   ${region}: ${count}`);
  });
  
  console.log(`\nEnhanced features: ${personas[0].enhanced.features.length}`);
  console.log('Features:', personas[0].enhanced.features.join(', '));
  
  // Validate personas
  console.log('\n🔍 Validating enhanced personas...');
  let validCount = 0;
  let enhancedCount = 0;
  
  for (const persona of personas) {
    if (persona.id && persona.userAgent && persona.webgl && persona.enhanced) {
      validCount++;
      if (persona.enhanced.version === '2.0') {
        enhancedCount++;
      }
    }
  }
  
  console.log(`✅ ${validCount}/${personas.length} personas are valid`);
  console.log(`🚀 ${enhancedCount}/${personas.length} personas have enhanced features`);
  
  if (validCount === personas.length && enhancedCount === personas.length) {
    console.log('\n🎯 All personas regenerated successfully with enhanced features!');
    console.log('   ✅ Google login detection evasion ready');
    console.log('   ✅ Enhanced WebGL fingerprinting');
    console.log('   ✅ Canvas noise protection');
    console.log('   ✅ Navigator spoofing');
    console.log('   ✅ Audio fingerprint protection');
    console.log('   ✅ Behavioral patterns simulation');
    console.log('   ✅ Timezone consistency');
    console.log('   ✅ HTTP headers spoofing');
  } else {
    console.log(`\n⚠️  ${personas.length - validCount} personas may have issues`);
  }
  
  console.log('\n🔄 To use the new personas, restart your application');
  console.log('💡 Test with: node test-enhanced-antidetect.js');
}

// Chạy script
if (require.main === module) {
  regenerateEnhancedPersonas().catch(console.error);
}

module.exports = { regenerateEnhancedPersonas };
