const ProxyService = require('./src/services/ProxyService');

async function quickProxyTest() {
  const proxyService = new ProxyService();
  
  console.log('🚀 Quick proxy test with improved error handling...\n');
  console.log('⏱️  Timeout:', proxyService.retryConfig.timeout / 1000, 'seconds');
  console.log('🔁 Max retries:', proxyService.retryConfig.maxRetries);
  console.log('');

  // Simple test cases
  const testCases = [
    {
      name: 'Direct Connection',
      proxy: { type: 'No proxy' },
      shouldWork: true
    },
    {
      name: 'Invalid HTTP Proxy',
      proxy: {
        type: 'HTTP',
        host: '192.168.1.999', // Invalid IP
        port: 8080
      },
      shouldWork: false
    },
    {
      name: 'Unreachable HTTP Proxy',
      proxy: {
        type: 'HTTP',
        host: '********', // Private IP that should be unreachable
        port: 8080
      },
      shouldWork: false
    }
  ];

  for (const testCase of testCases) {
    console.log(`📋 Testing: ${testCase.name}`);
    
    try {
      const startTime = Date.now();
      const result = await proxyService.testProxyConnection(testCase.proxy, 'ip-api');
      const duration = Date.now() - startTime;
      
      if (result.isActive) {
        console.log(`   ✅ Success! (${duration}ms)`);
        if (result.result) {
          console.log(`   📍 IP: ${result.result.ip}`);
          console.log(`   🌍 Location: ${result.result.city}, ${result.result.country}`);
        }
      } else {
        console.log(`   ❌ Failed: ${result.error} (${duration}ms)`);
        if (testCase.shouldWork) {
          console.log(`   ⚠️  Expected to work but failed`);
        } else {
          console.log(`   ✅ Expected failure - good error handling!`);
        }
      }
    } catch (error) {
      console.log(`   ❌ Exception: ${error.message}`);
    }
    
    console.log(`   ${'─'.repeat(50)}`);
  }

  console.log('\n✅ Quick test completed!');
}

// Run the test
if (require.main === module) {
  quickProxyTest().catch(console.error);
}

module.exports = quickProxyTest;
