/**
 * Enhanced Antidetect System Test
 * Test các tính năng mới được thêm vào để tránh Google detection
 */

const { chromium } = require('playwright');
const AntidetectManager = require('./src/antidetect/antidetect-manager');
const HumanBehavior = require('./src/antidetect/human-behavior');

async function testEnhancedAntidetect() {
  console.log('🧪 Testing Enhanced Antidetect System...\n');

  const antidetectManager = new AntidetectManager();

  try {
    // 1. Test persona loading
    console.log('1. Loading personas...');
    await antidetectManager.loadPersonas();
    console.log(`✅ Loaded ${antidetectManager.personas.length} personas\n`);

    // 2. Test enhanced persona selection
    console.log('2. Testing enhanced persona selection...');
    const testProxy = {
      ip: '*************',
      port: '8080',
      country: 'US',
      city: 'New York',
      type: 'HTTP'
    };

    const persona = await antidetectManager.selectRandomPersona('US');
    console.log(`✅ Selected persona: ${persona.id}`);
    console.log(`   Platform: ${persona.platform}`);
    console.log(`   User-Agent: ${persona.userAgent.substring(0, 80)}...`);
    console.log(`   WebGL Vendor: ${persona.webgl.vendor}`);
    console.log(`   WebGL Renderer: ${persona.webgl.renderer}`);
    console.log(`   Max Texture Size: ${persona.webgl.maxTextureSize || 'N/A'}`);
    console.log(`   Hardware Concurrency: ${persona.hardware.hardwareConcurrency}\n`);

    // 3. Test enhanced locale generation
    console.log('3. Testing enhanced locale generation...');
    const localeInfo = antidetectManager.generateLocaleFromProxy(testProxy);
    console.log(`✅ Generated locale info:`);
    console.log(`   Timezone: ${localeInfo.timezone}`);
    console.log(`   Locale: ${localeInfo.locale}`);
    console.log(`   Languages: ${localeInfo.languages.join(', ')}`);
    console.log(`   Country: ${localeInfo.country}`);
    console.log(`   Region: ${localeInfo.region}\n`);

    // 4. Test Google-specific context options
    console.log('4. Testing Google-specific context options...');
    const googleContextOptions = antidetectManager.createGoogleContextOptions(persona, testProxy);
    console.log(`✅ Google context options created:`);
    console.log(`   User-Agent: ${googleContextOptions.userAgent.substring(0, 60)}...`);
    console.log(`   Timezone: ${googleContextOptions.timezoneId}`);
    console.log(`   Locale: ${googleContextOptions.locale}`);
    console.log(`   Has Sec-GPC header: ${!!googleContextOptions.extraHTTPHeaders['Sec-GPC']}`);
    console.log(`   Has sec-ch-ua header: ${!!googleContextOptions.extraHTTPHeaders['sec-ch-ua']}`);
    console.log(`   Bypass CSP: ${googleContextOptions.bypassCSP}\n`);

    // 5. Test enhanced spoofing script
    console.log('5. Testing enhanced spoofing script...');
    const spoofingScript = antidetectManager.createSpoofingScript(persona);
    console.log(`✅ Enhanced spoofing script generated (${spoofingScript.length} characters)`);
    console.log(`   Contains WebGL spoofing: ${spoofingScript.includes('WebGL')}`);
    console.log(`   Contains Navigator spoofing: ${spoofingScript.includes('navigator.webdriver')}`);
    console.log(`   Contains Chrome runtime spoofing: ${spoofingScript.includes('chrome.runtime')}`);
    console.log(`   Contains automation removal: ${spoofingScript.includes('cdc_adoQpoasnfa76pfcZLmcfl')}`);
    console.log(`   Contains Google evasion: ${spoofingScript.includes('google.accounts')}`);
    console.log(`   Contains AudioContext protection: ${spoofingScript.includes('AudioContext')}\n`);

    // 6. Test enhanced browser launch options
    console.log('6. Testing enhanced browser launch options...');
    const launchOptions = antidetectManager.createBrowserLaunchOptions();
    console.log(`✅ Enhanced launch options:`);
    console.log(`   Total args: ${launchOptions.args.length}`);
    console.log(`   Has automation disable: ${launchOptions.args.includes('--disable-automation')}`);
    console.log(`   Has webdriver disable: ${launchOptions.args.includes('--disable-blink-features=AutomationControlled')}`);
    console.log(`   Has stealth flags: ${launchOptions.args.includes('--exclude-switches=enable-automation')}`);
    console.log(`   Has Google-specific flags: ${launchOptions.args.includes('--disable-features=UserAgentClientHint')}\n`);

    // 7. Test browser launch with real detection sites (optional)
    console.log('7. Testing browser launch with detection sites...');
    console.log('   Launching browser for manual testing...');
    
    const browser = await chromium.launch({
      ...launchOptions,
      headless: false
    });

    const context = await browser.newContext(googleContextOptions);
    await context.addInitScript(spoofingScript);
    
    const page = await context.newPage();
    
    // Initialize human behavior
    const humanBehavior = new HumanBehavior(page);
    
    console.log('✅ Browser launched successfully!');
    console.log('   Testing detection sites...\n');

    // Test detection sites
    const testSites = [
      { name: 'BrowserLeaks WebGL', url: 'https://browserleaks.com/webgl' },
      { name: 'BrowserLeaks Canvas', url: 'https://browserleaks.com/canvas' },
      { name: 'Detect Automation', url: 'https://bot.sannysoft.com/' },
      { name: 'PixelScan', url: 'https://pixelscan.net/' }
    ];

    for (const site of testSites) {
      try {
        console.log(`   Testing ${site.name}...`);
        await page.goto(site.url, { waitUntil: 'networkidle', timeout: 10000 });
        
        // Simulate human behavior
        await humanBehavior.explorePage(2000);
        await humanBehavior.randomDelay(1000, 500);
        
        console.log(`   ✅ ${site.name} loaded successfully`);
      } catch (error) {
        console.log(`   ⚠️  ${site.name} failed to load: ${error.message}`);
      }
    }

    console.log('\n🎉 Enhanced Antidetect System Test Completed!');
    console.log('\nManual verification steps:');
    console.log('1. Check WebGL fingerprint on browserleaks.com/webgl');
    console.log('2. Verify Canvas fingerprint on browserleaks.com/canvas');
    console.log('3. Check automation detection on bot.sannysoft.com');
    console.log('4. Test Google login on accounts.google.com');
    console.log('5. Verify timezone and locale consistency');
    console.log('\nPress any key to close browser and exit...');

    // Wait for user input
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.on('data', async () => {
      await browser.close();
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testEnhancedAntidetect();
}

module.exports = { testEnhancedAntidetect };
