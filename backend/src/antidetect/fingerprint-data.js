/**
 * Fingerprint Data Collection
 * <PERSON>hu thập dữ liệu vân tay thực tế cho việc tạo personas
 */

// <PERSON><PERSON> sách User-Agent thực tế cho Chrome trên Windows 10/11 (Updated 2025)
const WINDOWS_USER_AGENTS = [
  // Chrome 121-122 (Latest)
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/121.0.0.0 Safari/537.36',

  // Chrome 120 (Stable)
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',

  // Chrome 119 (Previous stable)
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',

  // Edge (Chromium-based)
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 Edg/122.0.0.0',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36 Edg/121.0.0.0',
  'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 Edg/122.0.0.0',

  // Firefox (for diversity)
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0) Gecko/20100101 Firefox/122.0',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
  'Mozilla/5.0 (Windows NT 11.0; Win64; x64; rv:122.0) Gecko/20100101 Firefox/122.0',

  // Some with different architectures for realism
  'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36 OPR/107.0.0.0'
];

// Danh sách User-Agent thực tế cho Chrome trên macOS (Updated 2025)
const MACOS_USER_AGENTS = [
  // Chrome 121-122 (Latest) on macOS Sonoma/Ventura
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 14_3_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 14_3_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 13_6_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 13_6_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',

  // Chrome 120 (Stable)
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 14_2_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 13_6_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',

  // Apple Silicon Macs
  'Mozilla/5.0 (Macintosh; Apple Mac OS X 14_3_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Apple Mac OS X 14_3_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Apple Mac OS X 13_6_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',

  // Safari (for diversity)
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.1.15',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 14_3_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.1.15',

  // Firefox on macOS
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:122.0) Gecko/20100101 Firefox/122.0',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 14.3; rv:122.0) Gecko/20100101 Firefox/122.0',

  // Edge on macOS
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 Edg/122.0.0.0'
];

// Độ phân giải màn hình phổ biến
const SCREEN_RESOLUTIONS = [
  { width: 1920, height: 1080, name: 'Full HD' },
  { width: 1366, height: 768, name: 'HD' },
  { width: 1536, height: 864, name: 'HD+' },
  { width: 2560, height: 1440, name: '2K' },
  { width: 3840, height: 2160, name: '4K' },
  { width: 1440, height: 900, name: 'WXGA+' },
  { width: 1600, height: 900, name: 'HD+' },
  { width: 1280, height: 720, name: 'HD Ready' },
  { width: 2560, height: 1600, name: 'WQXGA' },
  { width: 1680, height: 1050, name: 'WSXGA+' }
];

// WebGL Vendors và Renderers cho NVIDIA (Enhanced 2025)
const NVIDIA_WEBGL_CONFIGS = [
  {
    vendor: 'Google Inc. (NVIDIA)',
    renderer: 'ANGLE (NVIDIA, NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0, D3D11)',
    version: 'WebGL 1.0 (OpenGL ES 2.0 Chromium)',
    shadingLanguageVersion: 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)',
    maxTextureSize: 16384,
    maxVertexAttribs: 16,
    maxVaryingVectors: 30,
    aliasedLineWidthRange: [1, 1],
    aliasedPointSizeRange: [1, 1024]
  },
  {
    vendor: 'Google Inc. (NVIDIA)',
    renderer: 'ANGLE (NVIDIA, NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0, D3D11)',
    version: 'WebGL 1.0 (OpenGL ES 2.0 Chromium)',
    shadingLanguageVersion: 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)',
    maxTextureSize: 16384,
    maxVertexAttribs: 16,
    maxVaryingVectors: 30,
    aliasedLineWidthRange: [1, 1],
    aliasedPointSizeRange: [1, 1024]
  },
  {
    vendor: 'Google Inc. (NVIDIA)',
    renderer: 'ANGLE (NVIDIA, NVIDIA GeForce GTX 1660 SUPER Direct3D11 vs_5_0 ps_5_0, D3D11)',
    version: 'WebGL 1.0 (OpenGL ES 2.0 Chromium)',
    shadingLanguageVersion: 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)',
    maxTextureSize: 16384,
    maxVertexAttribs: 16,
    maxVaryingVectors: 30,
    aliasedLineWidthRange: [1, 1],
    aliasedPointSizeRange: [1, 1024]
  },
  {
    vendor: 'Google Inc. (NVIDIA)',
    renderer: 'ANGLE (NVIDIA, NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0, D3D11)',
    version: 'WebGL 1.0 (OpenGL ES 2.0 Chromium)',
    shadingLanguageVersion: 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)',
    maxTextureSize: 16384,
    maxVertexAttribs: 16,
    maxVaryingVectors: 30,
    aliasedLineWidthRange: [1, 1],
    aliasedPointSizeRange: [1, 1024]
  }
];

// WebGL Vendors và Renderers cho AMD (Enhanced 2025)
const AMD_WEBGL_CONFIGS = [
  {
    vendor: 'Google Inc. (AMD)',
    renderer: 'ANGLE (AMD, AMD Radeon RX 7700 XT Direct3D11 vs_5_0 ps_5_0, D3D11)',
    version: 'WebGL 1.0 (OpenGL ES 2.0 Chromium)',
    shadingLanguageVersion: 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)',
    maxTextureSize: 16384,
    maxVertexAttribs: 16,
    maxVaryingVectors: 30,
    aliasedLineWidthRange: [1, 1],
    aliasedPointSizeRange: [1, 1024]
  },
  {
    vendor: 'Google Inc. (AMD)',
    renderer: 'ANGLE (AMD, AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0, D3D11)',
    version: 'WebGL 1.0 (OpenGL ES 2.0 Chromium)',
    shadingLanguageVersion: 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)',
    maxTextureSize: 16384,
    maxVertexAttribs: 16,
    maxVaryingVectors: 30,
    aliasedLineWidthRange: [1, 1],
    aliasedPointSizeRange: [1, 1024]
  },
  {
    vendor: 'Google Inc. (AMD)',
    renderer: 'ANGLE (AMD, AMD Radeon RX 580 Series Direct3D11 vs_5_0 ps_5_0, D3D11)',
    version: 'WebGL 1.0 (OpenGL ES 2.0 Chromium)',
    shadingLanguageVersion: 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)',
    maxTextureSize: 16384,
    maxVertexAttribs: 16,
    maxVaryingVectors: 30,
    aliasedLineWidthRange: [1, 1],
    aliasedPointSizeRange: [1, 1024]
  }
];

// WebGL Vendors và Renderers cho Intel
const INTEL_WEBGL_CONFIGS = [
  {
    vendor: 'Google Inc. (Intel)',
    renderer: 'ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)',
    version: 'WebGL 1.0 (OpenGL ES 2.0 Chromium)',
    shadingLanguageVersion: 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)'
  },
  {
    vendor: 'Google Inc. (Intel)',
    renderer: 'ANGLE (Intel, Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0, D3D11)',
    version: 'WebGL 1.0 (OpenGL ES 2.0 Chromium)',
    shadingLanguageVersion: 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)'
  }
];

// WebGL Vendors và Renderers cho Apple (macOS)
const APPLE_WEBGL_CONFIGS = [
  {
    vendor: 'Google Inc. (Apple)',
    renderer: 'ANGLE (Apple, Apple M1, OpenGL 4.1)',
    version: 'WebGL 1.0 (OpenGL ES 2.0 Chromium)',
    shadingLanguageVersion: 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)'
  },
  {
    vendor: 'Google Inc. (Apple)',
    renderer: 'ANGLE (Apple, Apple M2, OpenGL 4.1)',
    version: 'WebGL 1.0 (OpenGL ES 2.0 Chromium)',
    shadingLanguageVersion: 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)'
  },
  {
    vendor: 'Google Inc. (Apple)',
    renderer: 'ANGLE (Apple, AMD Radeon Pro 5500M, OpenGL 4.1)',
    version: 'WebGL 1.0 (OpenGL ES 2.0 Chromium)',
    shadingLanguageVersion: 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)'
  }
];

// Fonts mặc định trên Windows
const WINDOWS_FONTS = [
  'Arial', 'Arial Black', 'Bahnschrift', 'Calibri', 'Cambria', 'Cambria Math',
  'Candara', 'Comic Sans MS', 'Consolas', 'Constantia', 'Corbel', 'Courier New',
  'Ebrima', 'Franklin Gothic Medium', 'Gabriola', 'Gadugi', 'Georgia', 'HoloLens MDL2 Assets',
  'Impact', 'Ink Free', 'Javanese Text', 'Leelawadee UI', 'Lucida Console', 'Lucida Sans Unicode',
  'Malgun Gothic', 'Marlett', 'Microsoft Himalaya', 'Microsoft JhengHei', 'Microsoft New Tai Lue',
  'Microsoft PhagsPa', 'Microsoft Sans Serif', 'Microsoft Tai Le', 'Microsoft YaHei',
  'Microsoft Yi Baiti', 'MingLiU-ExtB', 'Mongolian Baiti', 'MS Gothic', 'MV Boli',
  'Myanmar Text', 'Nirmala UI', 'Palatino Linotype', 'Segoe MDL2 Assets', 'Segoe Print',
  'Segoe Script', 'Segoe UI', 'Segoe UI Historic', 'Segoe UI Emoji', 'Segoe UI Symbol',
  'SimSun', 'Sitka', 'Sylfaen', 'Symbol', 'Tahoma', 'Times New Roman', 'Trebuchet MS',
  'Verdana', 'Webdings', 'Wingdings', 'Yu Gothic'
];

// Fonts mặc định trên macOS
const MACOS_FONTS = [
  'American Typewriter', 'Andale Mono', 'Arial', 'Arial Black', 'Arial Narrow', 'Arial Rounded MT Bold',
  'Arial Unicode MS', 'Avenir', 'Avenir Next', 'Avenir Next Condensed', 'Baskerville', 'Big Caslon',
  'Bodoni 72', 'Bodoni 72 Oldstyle', 'Bodoni 72 Smallcaps', 'Bradley Hand', 'Brush Script MT',
  'Chalkboard', 'Chalkboard SE', 'Chalkduster', 'Charter', 'Cochin', 'Comic Sans MS',
  'Copperplate', 'Courier', 'Courier New', 'Didot', 'DIN Alternate', 'DIN Condensed',
  'Futura', 'Geneva', 'Georgia', 'Gill Sans', 'Helvetica', 'Helvetica Neue', 'Herculanum',
  'Hoefler Text', 'Impact', 'Lucida Grande', 'Luminari', 'Marker Felt', 'Menlo', 'Monaco',
  'Noteworthy', 'Optima', 'Palatino', 'Papyrus', 'Phosphate', 'Rockwell', 'Savoye LET',
  'SignPainter', 'Skia', 'Snell Roundhand', 'Tahoma', 'Times', 'Times New Roman',
  'Trattatello', 'Trebuchet MS', 'Verdana', 'Zapfino'
];

// Múi giờ theo vùng địa lý
const TIMEZONE_BY_REGION = {
  'US': [
    'America/New_York', 'America/Chicago', 'America/Denver', 'America/Los_Angeles',
    'America/Anchorage', 'Pacific/Honolulu'
  ],
  'CA': [
    'America/Toronto', 'America/Vancouver', 'America/Montreal', 'America/Calgary',
    'America/Edmonton', 'America/Winnipeg'
  ],
  'GB': ['Europe/London'],
  'DE': ['Europe/Berlin'],
  'FR': ['Europe/Paris'],
  'IT': ['Europe/Rome'],
  'ES': ['Europe/Madrid'],
  'AU': [
    'Australia/Sydney', 'Australia/Melbourne', 'Australia/Brisbane',
    'Australia/Perth', 'Australia/Adelaide'
  ],
  'JP': ['Asia/Tokyo'],
  'KR': ['Asia/Seoul'],
  'CN': ['Asia/Shanghai'],
  'IN': ['Asia/Kolkata'],
  'BR': ['America/Sao_Paulo', 'America/Rio_Branco'],
  'MX': ['America/Mexico_City', 'America/Tijuana']
};

// Ngôn ngữ theo vùng địa lý
const LANGUAGE_BY_REGION = {
  'US': { primary: 'en-US', secondary: ['en'] },
  'CA': { primary: 'en-CA', secondary: ['en', 'fr-CA'] },
  'GB': { primary: 'en-GB', secondary: ['en'] },
  'DE': { primary: 'de-DE', secondary: ['de', 'en'] },
  'FR': { primary: 'fr-FR', secondary: ['fr', 'en'] },
  'IT': { primary: 'it-IT', secondary: ['it', 'en'] },
  'ES': { primary: 'es-ES', secondary: ['es', 'en'] },
  'AU': { primary: 'en-AU', secondary: ['en'] },
  'JP': { primary: 'ja-JP', secondary: ['ja', 'en'] },
  'KR': { primary: 'ko-KR', secondary: ['ko', 'en'] },
  'CN': { primary: 'zh-CN', secondary: ['zh', 'en'] },
  'IN': { primary: 'en-IN', secondary: ['en', 'hi'] },
  'BR': { primary: 'pt-BR', secondary: ['pt', 'en'] },
  'MX': { primary: 'es-MX', secondary: ['es', 'en'] }
};

module.exports = {
  WINDOWS_USER_AGENTS,
  MACOS_USER_AGENTS,
  SCREEN_RESOLUTIONS,
  NVIDIA_WEBGL_CONFIGS,
  AMD_WEBGL_CONFIGS,
  INTEL_WEBGL_CONFIGS,
  APPLE_WEBGL_CONFIGS,
  WINDOWS_FONTS,
  MACOS_FONTS,
  TIMEZONE_BY_REGION,
  LANGUAGE_BY_REGION
};
