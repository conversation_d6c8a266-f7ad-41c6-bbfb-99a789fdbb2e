/**
 * Human Behavior Simulation
 * Mô phỏng hành vi người dùng tự nhiên để tránh bot detection
 */

class HumanBehavior {
  constructor(page) {
    this.page = page;
    this.isActive = true;
  }

  /**
   * Simulate human-like typing with realistic delays
   * @param {string} selector - Element selector
   * @param {string} text - Text to type
   * @param {Object} options - Typing options
   */
  async humanType(selector, text, options = {}) {
    const element = await this.page.locator(selector);
    await element.click();
    
    // Clear existing text first
    await element.selectText();
    await this.page.keyboard.press('Delete');
    
    // Type with human-like delays
    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      await this.page.keyboard.type(char);
      
      // Random delay between keystrokes (50-200ms)
      const delay = Math.random() * 150 + 50;
      await this.page.waitForTimeout(delay);
      
      // Occasional longer pauses (thinking time)
      if (Math.random() < 0.1) {
        await this.page.waitForTimeout(Math.random() * 500 + 200);
      }
      
      // Simulate occasional typos and corrections
      if (Math.random() < 0.02 && i > 0) {
        await this.page.keyboard.press('Backspace');
        await this.page.waitForTimeout(Math.random() * 200 + 100);
        await this.page.keyboard.type(char);
        await this.page.waitForTimeout(Math.random() * 100 + 50);
      }
    }
  }

  /**
   * Simulate human-like mouse movement to element
   * @param {string} selector - Element selector
   * @param {Object} options - Movement options
   */
  async humanMove(selector, options = {}) {
    const element = await this.page.locator(selector);
    const box = await element.boundingBox();
    
    if (!box) return;
    
    // Get current mouse position (approximate)
    const currentPos = { x: 100, y: 100 }; // Default starting position
    
    // Calculate target position with some randomness
    const targetX = box.x + box.width / 2 + (Math.random() - 0.5) * 20;
    const targetY = box.y + box.height / 2 + (Math.random() - 0.5) * 20;
    
    // Move in steps to simulate human movement
    const steps = Math.floor(Math.random() * 10) + 5;
    for (let i = 0; i <= steps; i++) {
      const progress = i / steps;
      const x = currentPos.x + (targetX - currentPos.x) * progress;
      const y = currentPos.y + (targetY - currentPos.y) * progress;
      
      await this.page.mouse.move(x, y);
      await this.page.waitForTimeout(Math.random() * 20 + 10);
    }
  }

  /**
   * Simulate human-like clicking with movement
   * @param {string} selector - Element selector
   * @param {Object} options - Click options
   */
  async humanClick(selector, options = {}) {
    await this.humanMove(selector);
    
    // Small delay before clicking
    await this.page.waitForTimeout(Math.random() * 200 + 100);
    
    const element = await this.page.locator(selector);
    await element.click(options);
    
    // Small delay after clicking
    await this.page.waitForTimeout(Math.random() * 300 + 200);
  }

  /**
   * Simulate human-like scrolling
   * @param {Object} options - Scroll options
   */
  async humanScroll(options = {}) {
    const { direction = 'down', distance = 300, steps = 5 } = options;
    
    const stepDistance = distance / steps;
    
    for (let i = 0; i < steps; i++) {
      const delta = direction === 'down' ? stepDistance : -stepDistance;
      await this.page.mouse.wheel(0, delta);
      
      // Random delay between scroll steps
      await this.page.waitForTimeout(Math.random() * 200 + 100);
    }
    
    // Pause after scrolling (reading time)
    await this.page.waitForTimeout(Math.random() * 1000 + 500);
  }

  /**
   * Simulate reading behavior (pauses and small movements)
   * @param {number} duration - Reading duration in ms
   */
  async simulateReading(duration = 3000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < duration && this.isActive) {
      // Small random mouse movements
      const currentPos = await this.page.evaluate(() => {
        return { x: window.mouseX || 500, y: window.mouseY || 300 };
      });
      
      const newX = currentPos.x + (Math.random() - 0.5) * 50;
      const newY = currentPos.y + (Math.random() - 0.5) * 30;
      
      await this.page.mouse.move(newX, newY);
      
      // Random pause
      await this.page.waitForTimeout(Math.random() * 1000 + 500);
      
      // Occasional small scroll
      if (Math.random() < 0.3) {
        await this.page.mouse.wheel(0, (Math.random() - 0.5) * 100);
      }
    }
  }

  /**
   * Simulate form filling behavior
   * @param {Array} fields - Array of field objects {selector, value}
   */
  async fillFormHumanLike(fields) {
    for (const field of fields) {
      // Look around before focusing on field
      await this.simulateReading(Math.random() * 1000 + 500);
      
      // Move to field and click
      await this.humanClick(field.selector);
      
      // Type the value
      await this.humanType(field.selector, field.value);
      
      // Small pause after filling field
      await this.page.waitForTimeout(Math.random() * 1000 + 500);
    }
  }

  /**
   * Simulate page exploration (looking around)
   * @param {number} duration - Exploration duration in ms
   */
  async explorePage(duration = 5000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < duration && this.isActive) {
      // Random mouse movements
      const x = Math.random() * 800 + 100;
      const y = Math.random() * 600 + 100;
      
      await this.page.mouse.move(x, y);
      await this.page.waitForTimeout(Math.random() * 1500 + 500);
      
      // Occasional scroll
      if (Math.random() < 0.4) {
        await this.humanScroll({
          direction: Math.random() < 0.5 ? 'down' : 'up',
          distance: Math.random() * 200 + 100,
          steps: Math.floor(Math.random() * 3) + 2
        });
      }
    }
  }

  /**
   * Add random delays to make actions seem more human
   * @param {number} baseDelay - Base delay in ms
   * @param {number} variance - Variance in ms
   */
  async randomDelay(baseDelay = 1000, variance = 500) {
    const delay = baseDelay + (Math.random() - 0.5) * variance;
    await this.page.waitForTimeout(Math.max(100, delay));
  }

  /**
   * Simulate hesitation before important actions
   * @param {string} actionType - Type of action (click, submit, etc.)
   */
  async simulateHesitation(actionType = 'click') {
    // Longer hesitation for important actions
    const hesitationTime = actionType === 'submit' ? 
      Math.random() * 2000 + 1000 : 
      Math.random() * 1000 + 500;
    
    // Small mouse movements during hesitation
    for (let i = 0; i < 3; i++) {
      const currentPos = await this.page.evaluate(() => {
        return { x: window.mouseX || 500, y: window.mouseY || 300 };
      });
      
      await this.page.mouse.move(
        currentPos.x + (Math.random() - 0.5) * 20,
        currentPos.y + (Math.random() - 0.5) * 20
      );
      
      await this.page.waitForTimeout(hesitationTime / 3);
    }
  }

  /**
   * Stop all human behavior simulation
   */
  stop() {
    this.isActive = false;
  }

  /**
   * Start human behavior simulation
   */
  start() {
    this.isActive = true;
  }
}

module.exports = HumanBehavior;
