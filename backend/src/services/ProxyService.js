const { chromium } = require('playwright');
const net = require('net');
const { SocksProxyAgent } = require('socks-proxy-agent');

class ProxyService {
  constructor() {
    this.ipCheckerEndpoints = {
      'IP2Location': 'https://api.ip2location.io/',
      'ip-api': 'http://ip-api.com/json/',
      'IPIDEA': 'https://api.ipidea.net/ip-location',
      'IPFoxy': 'https://api.ipfoxy.com/get',
      'IPinfo': 'https://ipinfo.io/json',
      'httpbin': 'https://httpbin.org/ip'
    };

    // Fallback order for IP checkers
    this.fallbackOrder = ['ip-api', 'httpbin', 'IPinfo', 'IP2Location', 'IPIDEA', 'IPFoxy'];

    // Retry configuration
    this.retryConfig = {
      maxRetries: 2,
      retryDelay: 1000, // 1 second
      timeout: 15000,   // 15 seconds
      connectTimeout: 5000 // 5 seconds for initial connectivity test
    };
  }

  /**
   * Test proxy connection with retry mechanism and fallback IP checkers
   */
  async testProxyConnection(proxy, ipChecker = 'ip-api') {
    let browser = null;
    let lastError = null;

    try {
      // Handle "No proxy" case
      if (proxy.type === 'No proxy') {
        return await this.testDirectConnection(ipChecker);
      }

      // Special handling for SOCKS5 with authentication
      if (proxy.type.toLowerCase() === 'socks5' && proxy.username && proxy.password) {
        return await this.testSocks5ProxyWithAuth(proxy, ipChecker);
      }

      // Test basic connectivity first for non-local proxies
      if (proxy.host !== '127.0.0.1' && proxy.host !== 'localhost') {
        console.log(`Testing basic connectivity to ${proxy.host}:${proxy.port}...`);
        const isConnectable = await this.testProxyConnectivity(proxy);
        if (!isConnectable) {
          return {
            isActive: false,
            result: null,
            error: `Cannot connect to proxy server ${proxy.host}:${proxy.port}`
          };
        }
        console.log(`✅ Basic connectivity OK`);
      }

      // Create proxy configuration based on type
      const proxyConfig = this.createProxyConfig(proxy);

      // Try with retry mechanism
      for (let attempt = 1; attempt <= this.retryConfig.maxRetries; attempt++) {
        try {
          console.log(`Attempt ${attempt}/${this.retryConfig.maxRetries} for proxy ${proxy.host}:${proxy.port}`);

          // Launch browser with proxy
          browser = await chromium.launch({
            headless: true,
            proxy: proxyConfig,
            timeout: this.retryConfig.timeout
          });

          const context = await browser.newContext({
            timeout: this.retryConfig.timeout,
            ignoreHTTPSErrors: true
          });

          const page = await context.newPage();
          page.setDefaultTimeout(this.retryConfig.timeout);

          // Test connection using selected IP checker with fallback
          const result = await this.checkIPWithServiceAndFallback(page, ipChecker);

          return {
            isActive: true,
            result: result,
            attempt: attempt
          };

        } catch (error) {
          lastError = error;
          console.error(`Attempt ${attempt} failed:`, error.message);

          // Close browser before retry
          if (browser) {
            try {
              await browser.close();
              browser = null;
            } catch (closeError) {
              console.error('Error closing browser:', closeError.message);
            }
          }

          // For certain errors, don't retry
          if (error.message.includes('Cannot connect to proxy server') ||
              error.message.includes('Invalid proxy configuration')) {
            console.log('❌ Proxy server unreachable, skipping retries');
            break;
          }

          // Wait before retry (except for last attempt)
          if (attempt < this.retryConfig.maxRetries) {
            console.log(`⏳ Waiting ${this.retryConfig.retryDelay}ms before retry...`);
            await this.sleep(this.retryConfig.retryDelay);
          }
        }
      }

      // All attempts failed
      // Special handling for SOCKS5 authentication error
      if (lastError && (lastError.message.includes('Browser does not support socks5 proxy authentication') ||
          lastError.message.includes('socks5 proxy authentication'))) {
        return await this.testSocks5ProxyWithAuth(proxy, ipChecker);
      }

      return {
        isActive: false,
        result: null,
        error: lastError ? lastError.message : 'All retry attempts failed'
      };

    } catch (error) {
      console.error('Proxy test error:', error.message);
      return {
        isActive: false,
        result: null,
        error: error.message
      };
    } finally {
      // Ensure browser is always closed
      if (browser) {
        try {
          await browser.close();
        } catch (closeError) {
          console.error('Error closing browser:', closeError.message);
        }
      }
    }
  }

  /**
   * Test direct connection (no proxy) with fallback IP checkers
   */
  async testDirectConnection(ipChecker) {
    let browser = null;
    try {
      browser = await chromium.launch({
        headless: true,
        timeout: this.retryConfig.timeout
      });

      const context = await browser.newContext({
        ignoreHTTPSErrors: true
      });
      const page = await context.newPage();
      page.setDefaultTimeout(this.retryConfig.timeout);

      const result = await this.checkIPWithServiceAndFallback(page, ipChecker);

      return {
        isActive: true,
        result: result
      };

    } catch (error) {
      console.error('Direct connection test error:', error.message);
      return {
        isActive: false,
        result: null,
        error: error.message
      };
    } finally {
      if (browser) {
        try {
          await browser.close();
        } catch (closeError) {
          console.error('Error closing browser:', closeError.message);
        }
      }
    }
  }

  /**
   * Sleep utility function
   */
  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Test SOCKS5 proxy with authentication using alternative method
   */
  async testSocks5ProxyWithAuth(proxy, ipChecker = 'ip-api') {
    try {
      console.log('Testing SOCKS5 proxy with authentication using fallback method...');

      // Test basic connectivity first
      const isConnectable = await this.testProxyConnectivity(proxy);
      if (!isConnectable) {
        return {
          isActive: false,
          result: null,
          error: 'Cannot connect to SOCKS5 proxy server'
        };
      }

      console.log('SOCKS5 proxy server is reachable, testing with HTTP request...');

      // Try to use SOCKS5 proxy with Node.js HTTP client
      const result = await this.testSocks5WithHttpClient(proxy, ipChecker);

      return {
        isActive: result.success,
        result: result.data,
        error: result.error
      };

    } catch (error) {
      console.error('SOCKS5 proxy test error:', error.message);
      return {
        isActive: false,
        result: null,
        error: error.message
      };
    }
  }

  /**
   * Test basic proxy connectivity
   */
  async testProxyConnectivity(proxy) {
    return new Promise((resolve) => {
      const socket = new net.Socket();
      const timeout = setTimeout(() => {
        socket.destroy();
        console.log(`❌ Connection timeout to ${proxy.host}:${proxy.port}`);
        resolve(false);
      }, this.retryConfig.connectTimeout);

      socket.connect(proxy.port, proxy.host, () => {
        clearTimeout(timeout);
        socket.destroy();
        console.log(`✅ TCP connection successful to ${proxy.host}:${proxy.port}`);
        resolve(true);
      });

      socket.on('error', (error) => {
        clearTimeout(timeout);
        console.log(`❌ Connection error to ${proxy.host}:${proxy.port}: ${error.message}`);
        resolve(false);
      });
    });
  }

  /**
   * Test SOCKS5 connectivity using HTTP client with SOCKS proxy agent
   */
  async testSocks5WithHttpClient(proxy, ipChecker) {
    const https = require('https');
    const http = require('http');

    try {
      // Use ip-api as it's more reliable and uses HTTP
      const endpoint = this.ipCheckerEndpoints[ipChecker] || this.ipCheckerEndpoints['ip-api'];
      const isHttps = endpoint.startsWith('https');

      // Create SOCKS proxy agent
      const proxyUrl = `socks5://${proxy.username}:${proxy.password}@${proxy.host}:${proxy.port}`;
      const agent = new SocksProxyAgent(proxyUrl);

      const options = {
        agent: agent,
        timeout: this.retryConfig.timeout,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      };

      return new Promise((resolve) => {
        const client = isHttps ? https : http;

        const req = client.get(endpoint, options, (res) => {
          let data = '';

          res.on('data', (chunk) => {
            data += chunk;
          });

          res.on('end', () => {
            try {
              const jsonData = JSON.parse(data);
              const normalizedData = this.normalizeIPData(jsonData, ipChecker);

              resolve({
                success: true,
                data: normalizedData,
                error: null
              });
            } catch (parseError) {
              resolve({
                success: false,
                data: null,
                error: `Failed to parse response: ${parseError.message}`
              });
            }
          });
        });

        req.on('error', (error) => {
          resolve({
            success: false,
            data: null,
            error: `HTTP request failed: ${error.message}`
          });
        });

        req.on('timeout', () => {
          req.destroy();
          resolve({
            success: false,
            data: null,
            error: 'Request timeout'
          });
        });

        req.setTimeout(this.retryConfig.timeout);
      });

    } catch (error) {
      return {
        success: false,
        data: null,
        error: error.message
      };
    }
  }

  /**
   * Create proxy configuration based on proxy type
   */
  createProxyConfig(proxy) {
    if (!proxy || !proxy.host || !proxy.port) {
      throw new Error('Invalid proxy configuration: host and port are required');
    }

    let server;
    const proxyType = proxy.type.toLowerCase();

    switch (proxyType) {
      case 'http':
        server = `http://${proxy.host}:${proxy.port}`;
        break;
      case 'https':
        server = `https://${proxy.host}:${proxy.port}`;
        break;
      case 'socks5':
        // For SOCKS5 without auth, Playwright can handle it
        if (!proxy.username || !proxy.password) {
          server = `socks5://${proxy.host}:${proxy.port}`;
        } else {
          // For SOCKS5 with auth, we'll handle it separately
          throw new Error('Browser does not support socks5 proxy authentication');
        }
        break;
      case 'socks4':
        server = `socks4://${proxy.host}:${proxy.port}`;
        break;
      case 'ssh':
        // SSH tunneling would require additional setup
        // For now, treat as HTTP proxy
        console.warn('SSH proxy type detected, treating as HTTP proxy');
        server = `http://${proxy.host}:${proxy.port}`;
        break;
      default:
        console.warn(`Unknown proxy type: ${proxy.type}, treating as HTTP proxy`);
        server = `http://${proxy.host}:${proxy.port}`;
    }

    const config = {
      server,
      // Add bypass for local addresses
      bypass: 'localhost,127.0.0.1,::1'
    };

    // Add authentication for HTTP/HTTPS proxies
    if ((proxyType === 'http' || proxyType === 'https') && proxy.username && proxy.password) {
      config.username = proxy.username;
      config.password = proxy.password;
    }

    // Add authentication for SOCKS4 (if supported)
    if (proxyType === 'socks4' && proxy.username) {
      config.username = proxy.username;
      // SOCKS4 doesn't support password authentication
    }

    console.log(`Created proxy config for ${proxyType}: ${server}`);
    return config;
  }

  /**
   * Check IP using selected service with fallback mechanism
   */
  async checkIPWithServiceAndFallback(page, preferredChecker) {
    // Create list of checkers to try, starting with preferred
    const checkersToTry = [preferredChecker, ...this.fallbackOrder.filter(c => c !== preferredChecker)];

    let lastError = null;

    for (const ipChecker of checkersToTry) {
      try {
        console.log(`Trying IP checker: ${ipChecker}`);
        const result = await this.checkIPWithService(page, ipChecker);
        console.log(`Successfully got IP info from ${ipChecker}`);
        return result;
      } catch (error) {
        console.error(`Failed to check IP with ${ipChecker}:`, error.message);
        lastError = error;
        // Continue to next checker
      }
    }

    // All checkers failed
    throw new Error(`All IP checkers failed. Last error: ${lastError ? lastError.message : 'Unknown'}`);
  }

  /**
   * Check IP using selected service
   */
  async checkIPWithService(page, ipChecker) {
    const endpoint = this.ipCheckerEndpoints[ipChecker];
    if (!endpoint) {
      throw new Error(`Unknown IP checker: ${ipChecker}`);
    }

    try {
      // Check if page is still valid
      if (page.isClosed()) {
        throw new Error('Page has been closed');
      }

      const response = await page.goto(endpoint, {
        waitUntil: 'domcontentloaded',
        timeout: this.retryConfig.timeout
      });

      if (!response || response.status() !== 200) {
        throw new Error(`Failed to fetch from ${ipChecker} (Status: ${response ? response.status() : 'No response'})`);
      }

      // Get the response text
      const content = await page.content();
      const jsonMatch = content.match(/<pre[^>]*>(.*?)<\/pre>/s) ||
                       content.match(/<body[^>]*>(.*?)<\/body>/s);

      let jsonText = jsonMatch ? jsonMatch[1].trim() : content;

      // Try to extract JSON from the page
      try {
        const data = JSON.parse(jsonText);
        return this.normalizeIPData(data, ipChecker);
      } catch (parseError) {
        // If JSON parsing fails, try to extract from page text
        const textContent = await page.textContent('body');
        try {
          const data = JSON.parse(textContent);
          return this.normalizeIPData(data, ipChecker);
        } catch (secondParseError) {
          throw new Error(`Failed to parse response from ${ipChecker}: ${parseError.message}`);
        }
      }

    } catch (error) {
      console.error(`Error checking IP with ${ipChecker}:`, error.message);
      throw error;
    }
  }

  /**
   * Normalize IP data from different services
   */
  normalizeIPData(data, ipChecker) {
    switch (ipChecker) {
      case 'IP2Location':
        return {
          ip: data.ip,
          country: data.country_name || data.country,
          region: data.region_name || data.region,
          city: data.city_name || data.city,
          timezone: data.time_zone,
          isp: data.isp
        };
      case 'ip-api':
        return {
          ip: data.query || data.ip,
          country: data.country,
          region: data.regionName || data.region,
          city: data.city,
          timezone: data.timezone,
          isp: data.isp
        };
      case 'IPIDEA':
        return {
          ip: data.ip,
          country: data.country,
          region: data.region,
          city: data.city,
          timezone: data.timezone,
          isp: data.isp
        };
      case 'IPFoxy':
        return {
          ip: data.ip,
          country: data.country,
          region: data.region,
          city: data.city,
          timezone: data.timezone,
          isp: data.isp
        };
      case 'IPinfo':
        return {
          ip: data.ip,
          country: data.country,
          region: data.region,
          city: data.city,
          timezone: data.timezone,
          isp: data.org
        };
      case 'httpbin':
        return {
          ip: data.origin,
          country: 'Unknown',
          region: 'Unknown',
          city: 'Unknown',
          timezone: 'Unknown',
          isp: 'Unknown'
        };
      default:
        return {
          ip: data.ip || data.query || data.origin,
          country: data.country || data.country_name || 'Unknown',
          region: data.region || data.regionName || data.region_name || 'Unknown',
          city: data.city || data.city_name || 'Unknown',
          timezone: data.timezone || data.time_zone || 'Unknown',
          isp: data.isp || data.org || 'Unknown'
        };
    }
  }
}

module.exports = ProxyService;
