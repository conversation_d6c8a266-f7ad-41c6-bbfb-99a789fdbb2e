const AccountController = require('../controllers/AccountController');
const ProxyService = require('../services/ProxyService');
const AutomationService = require('../services/AutomationService');
const SettingsService = require('../services/SettingsService');

class CommandRouter {
  constructor(dbManager, wsServer, loginAutomation, followInteractAutomation) {
    this.dbManager = dbManager;
    this.wsServer = wsServer;
    this.loginAutomation = loginAutomation;
    this.followInteractAutomation = followInteractAutomation;

    // Initialize controllers and services
    this.accountController = new AccountController(dbManager, wsServer);
    this.proxyService = new ProxyService();
    this.automationService = new AutomationService(dbManager, wsServer, followInteractAutomation);
    this.settingsService = new SettingsService(dbManager, wsServer);
  }

  /**
   * Register all command handlers
   */
  registerCommands() {
    // Account commands
    this.wsServer.onCommand('get_accounts', (ws, data) => 
      this.accountController.getAccounts(ws, data));
    
    this.wsServer.onCommand('create_account', (ws, data) => 
      this.accountController.createAccount(ws, data));
    
    this.wsServer.onCommand('delete_account', (ws, data) => 
      this.accountController.deleteAccount(ws, data));
    
    this.wsServer.onCommand('login_account', (ws, data) => 
      this.accountController.loginAccount(ws, data, this.loginAutomation));
    
    this.wsServer.onCommand('login_accounts_batch', (ws, data) => 
      this.accountController.loginAccountsBatch(ws, data, this.loginAutomation));

    // Proxy commands
    this.wsServer.onCommand('test_proxy', async (ws, data) => {
      try {
        const { proxy, ipChecker } = data;

        if (!proxy || (!proxy.host && proxy.type !== 'No proxy')) {
          this.wsServer.sendError(ws, 'Proxy information is required');
          return;
        }

        this.wsServer.sendLog('info', `Testing proxy ${proxy.type}: ${proxy.host || 'local'}:${proxy.port || 'N/A'}`);

        // Test proxy connection with default IP checker
        const result = await this.proxyService.testProxyConnection(proxy, ipChecker || 'ip-api');
        
        // Send result back to client
        this.wsServer.sendToClient(ws, {
          type: 'proxy_test_result',
          isActive: result.isActive,
          result: result.result,
          proxy
        });

        this.wsServer.sendLog(
          result.isActive ? 'success' : 'error', 
          `Proxy ${proxy.type} ${proxy.host || 'local'}:${proxy.port || 'N/A'} is ${result.isActive ? 'active' : 'inactive'}`
        );

      } catch (error) {
        this.wsServer.sendError(ws, `Failed to test proxy: ${error.message}`);
        this.wsServer.sendToClient(ws, {
          type: 'proxy_test_result',
          isActive: false,
          proxy: data.proxy
        });
      }
    });

    // Automation commands
    this.wsServer.onCommand('start_automation', (ws, data) => 
      this.automationService.startAutomation(ws, data));
    
    this.wsServer.onCommand('stop_automation', (ws, data) => 
      this.automationService.stopAutomation(ws, data));
    
    this.wsServer.onCommand('get_automation_status', (ws, data) => 
      this.automationService.getAutomationStatus(ws, data));
    
    this.wsServer.onCommand('pause_automation', (ws, data) => 
      this.automationService.pauseAutomation(ws, data));

    // Settings commands
    this.wsServer.onCommand('get_settings', (ws, data) => 
      this.settingsService.getSettings(ws, data));
    
    this.wsServer.onCommand('update_settings', (ws, data) => 
      this.settingsService.updateSettings(ws, data));
    
    this.wsServer.onCommand('load_accounts', (ws, data) => 
      this.settingsService.loadAccounts(ws, data));
    
    this.wsServer.onCommand('load_proxies', (ws, data) => 
      this.settingsService.loadProxies(ws, data));
    
    this.wsServer.onCommand('load_comments', (ws, data) => 
      this.settingsService.loadComments(ws, data));

    console.log(`Command handlers registered`);
  }
}

module.exports = CommandRouter;
