const ProxyService = require('./src/services/ProxyService');

async function testSocks5Specific() {
  const proxyService = new ProxyService();
  
  console.log('🧪 Testing SOCKS5 proxy with specific configuration...\n');

  // Test case with the provided proxy information
  const testProxy = {
    type: 'SOCKS5',
    host: '***************',
    port: 13996,
    username: 'user301105',
    password: 'e538k7'
  };

  console.log('📋 Testing SOCKS5 Proxy:');
  console.log(`   Host: ${testProxy.host}:${testProxy.port}`);
  console.log(`   Auth: ${testProxy.username}:${'*'.repeat(testProxy.password.length)}`);
  console.log('');

  // Test with different IP checkers
  const ipCheckers = ['IP2Location', 'ip-api', 'httpbin', 'IPinfo'];

  for (const ipChecker of ipCheckers) {
    console.log(`🔄 Testing with ${ipChecker}...`);
    
    try {
      const startTime = Date.now();
      const result = await proxyService.testProxyConnection(testProxy, ipChecker);
      const duration = Date.now() - startTime;
      
      if (result.isActive) {
        console.log(`   ✅ Success with ${ipChecker}! (${duration}ms)`);
        if (result.result) {
          console.log(`   📍 IP: ${result.result.ip}`);
          console.log(`   🌍 Country: ${result.result.country}`);
          console.log(`   🏙️  City: ${result.result.city}`);
          console.log(`   🕐 Timezone: ${result.result.timezone}`);
          console.log(`   🌐 ISP: ${result.result.isp}`);
        }
        break; // Success, no need to try other checkers
      } else {
        console.log(`   ❌ Failed with ${ipChecker}: ${result.error} (${duration}ms)`);
      }
    } catch (error) {
      console.log(`   ❌ Exception with ${ipChecker}: ${error.message}`);
    }
    
    console.log('');
  }

  // Test basic connectivity separately
  console.log('🔌 Testing basic TCP connectivity...');
  try {
    const isConnectable = await proxyService.testProxyConnectivity(testProxy);
    if (isConnectable) {
      console.log('   ✅ TCP connection successful');
    } else {
      console.log('   ❌ TCP connection failed');
    }
  } catch (error) {
    console.log(`   ❌ TCP test error: ${error.message}`);
  }

  // Test SOCKS5 HTTP client directly
  console.log('\n🌐 Testing SOCKS5 HTTP client directly...');
  try {
    const result = await proxyService.testSocks5WithHttpClient(testProxy, 'ip-api');
    if (result.success) {
      console.log('   ✅ SOCKS5 HTTP client successful');
      if (result.data) {
        console.log(`   📍 IP: ${result.data.ip}`);
        console.log(`   🌍 Location: ${result.data.city}, ${result.data.country}`);
      }
    } else {
      console.log(`   ❌ SOCKS5 HTTP client failed: ${result.error}`);
    }
  } catch (error) {
    console.log(`   ❌ SOCKS5 HTTP client error: ${error.message}`);
  }

  console.log('\n📊 Test Summary:');
  console.log('• SOCKS5 proxy configuration validated');
  console.log('• Multiple IP checker services tested');
  console.log('• TCP connectivity verified');
  console.log('• HTTP client fallback tested');
  
  console.log('\n💡 Troubleshooting tips:');
  console.log('• If TCP connection fails: Check proxy server status');
  console.log('• If authentication fails: Verify username/password');
  console.log('• If IP checkers fail: Try different services');
  console.log('• If all fail: Proxy server may be down or blocked');
}

// Run the test
if (require.main === module) {
  testSocks5Specific().catch(console.error);
}

module.exports = testSocks5Specific;
